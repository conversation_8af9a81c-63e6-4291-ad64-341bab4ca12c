use std::sync::Arc;
use anyhow::Result;
use deadpool::managed::{Manager, Pool, PoolError};
use solana_client::nonblocking::rpc_client::RpcClient;
use anchor_client::solana_sdk::signature::Keypair;

// Import the existing AppState for compatibility
use crate::common::config::AppState as LegacyAppState;

use crate::core::{
    rate_limiter::{RateLimiter, RateLimiterConfig},
    circuit_breaker::{CircuitBreaker, CircuitBreakerConfig},
    lock_free_globals::LockFreeGlobals,
    retry::RetryConfig,
};

/// RPC connection manager for connection pooling
#[derive(Debug)]
pub struct RpcConnectionManager {
    endpoint: String,
}

impl RpcConnectionManager {
    pub fn new(endpoint: String) -> Self {
        Self { endpoint }
    }
}

#[async_trait::async_trait]
impl Manager for RpcConnectionManager {
    type Type = RpcClient;
    type Error = PoolError<anyhow::Error>;

    async fn create(&self) -> Result<Self::Type, Self::Error> {
        Ok(RpcClient::new(self.endpoint.clone()))
    }

    async fn recycle(&self, conn: &mut Self::Type, _: &deadpool::managed::Metrics) -> deadpool::managed::RecycleResult<Self::Error> {
        // Simple health check - try to get the latest blockhash
        match conn.get_latest_blockhash().await {
            Ok(_) => Ok(()),
            Err(e) => Err(deadpool::managed::RecycleError::Backend(PoolError::Backend(anyhow::anyhow!(
                "RPC health check failed: {}", e
            )))),
        }
    }
}

/// Configuration for connection pool
#[derive(Debug, Clone)]
pub struct PoolConfig {
    pub max_size: usize,
    pub min_size: usize,
    pub timeout_seconds: u64,
    pub idle_timeout_seconds: u64,
}

impl Default for PoolConfig {
    fn default() -> Self {
        Self {
            max_size: 20,
            min_size: 5,
            timeout_seconds: 15,
            idle_timeout_seconds: 600,
        }
    }
}

impl PoolConfig {
    /// Load configuration from environment variables
    pub fn from_env() -> Self {
        Self {
            max_size: std::env::var("RPC_POOL_MAX_SIZE")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(20),
            min_size: std::env::var("RPC_POOL_MIN_SIZE")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(5),
            timeout_seconds: std::env::var("RPC_POOL_TIMEOUT_SECONDS")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(15),
            idle_timeout_seconds: std::env::var("RPC_POOL_IDLE_TIMEOUT_SECONDS")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(600),
        }
    }
}

/// Enhanced application state with all improved components
/// This is the new AppState that includes rate limiting, circuit breakers, etc.
/// Use this for new code. The legacy AppState is still available for backward compatibility.
#[derive(Debug)]
pub struct EnhancedAppState {
    /// Lock-free global state management
    pub globals: Arc<LockFreeGlobals>,
    
    /// Rate limiter for controlling concurrent operations
    pub rate_limiter: Arc<RateLimiter>,
    
    /// Circuit breakers for different services
    pub rpc_circuit_breaker: Arc<CircuitBreaker>,
    pub swap_circuit_breaker: Arc<CircuitBreaker>,
    pub price_circuit_breaker: Arc<CircuitBreaker>,
    
    /// Connection pool for RPC clients
    pub rpc_pool: Pool<RpcConnectionManager>,
    
    /// Retry configurations for different operation types
    pub rpc_retry_config: RetryConfig,
    pub swap_retry_config: RetryConfig,
    pub price_retry_config: RetryConfig,
    
    /// Application keypair
    pub keypair: Arc<Keypair>,
    
    /// RPC endpoint
    pub rpc_endpoint: String,
}

impl EnhancedAppState {
    /// Create new application state with default configuration
    pub async fn new(keypair: Keypair, rpc_endpoint: String) -> Result<Self> {
        let pool_config = PoolConfig::default();
        let rate_limiter_config = RateLimiterConfig::default();
        let circuit_breaker_config = CircuitBreakerConfig::default();
        
        Self::with_config(
            keypair,
            rpc_endpoint,
            pool_config,
            rate_limiter_config,
            circuit_breaker_config,
        ).await
    }

    /// Create new application state from environment variables
    pub async fn from_env(keypair: Keypair, rpc_endpoint: String) -> Result<Self> {
        let pool_config = PoolConfig::from_env();
        let rate_limiter_config = RateLimiterConfig::from_env();
        let circuit_breaker_config = CircuitBreakerConfig::from_env();

        Self::with_config(
            keypair,
            rpc_endpoint,
            pool_config,
            rate_limiter_config,
            circuit_breaker_config,
        ).await
    }

    /// Create enhanced app state from legacy app state for migration
    pub async fn from_legacy(legacy_app_state: &LegacyAppState, rpc_endpoint: String) -> Result<Self> {
        // Extract keypair from legacy state
        let keypair = Keypair::from_bytes(&legacy_app_state.wallet.to_bytes())?;

        // Create enhanced state with environment configuration
        Self::from_env(keypair, rpc_endpoint).await
    }

    /// Create new application state with custom configuration
    pub async fn with_config(
        keypair: Keypair,
        rpc_endpoint: String,
        pool_config: PoolConfig,
        rate_limiter_config: RateLimiterConfig,
        circuit_breaker_config: CircuitBreakerConfig,
    ) -> Result<Self> {
        // Create connection pool
        let manager = RpcConnectionManager::new(rpc_endpoint.clone());
        let pool = Pool::builder(manager)
            .max_size(pool_config.max_size)
            .build()
            .map_err(|e| anyhow::anyhow!("Failed to create RPC pool: {}", e))?;

        // Create rate limiter
        let rate_limiter = Arc::new(RateLimiter::new(rate_limiter_config));

        // Create circuit breakers
        let rpc_circuit_breaker = Arc::new(CircuitBreaker::new(
            "rpc".to_string(),
            circuit_breaker_config.clone(),
        ));
        let swap_circuit_breaker = Arc::new(CircuitBreaker::new(
            "swap".to_string(),
            circuit_breaker_config.clone(),
        ));
        let price_circuit_breaker = Arc::new(CircuitBreaker::new(
            "price".to_string(),
            circuit_breaker_config,
        ));

        // Create global state
        let globals = Arc::new(LockFreeGlobals::from_env());

        Ok(Self {
            globals,
            rate_limiter,
            rpc_circuit_breaker,
            swap_circuit_breaker,
            price_circuit_breaker,
            rpc_pool: pool,
            rpc_retry_config: RetryConfig::for_rpc(),
            swap_retry_config: RetryConfig::for_swaps(),
            price_retry_config: RetryConfig::for_price_checks(),
            keypair: Arc::new(keypair),
            rpc_endpoint,
        })
    }

    /// Get an RPC client from the pool
    pub async fn get_rpc_client(&self) -> Result<deadpool::managed::Object<RpcConnectionManager>> {
        self.rpc_pool
            .get()
            .await
            .map_err(|e| anyhow::anyhow!("Failed to get RPC client from pool: {}", e))
    }

    /// Execute an RPC operation with full protection (rate limiting, circuit breaker, retry)
    pub async fn execute_rpc_protected<T>(&self, operation: impl Fn(Arc<RpcClient>) -> Result<T> + Send + Sync) -> Result<T>
    where
        T: Send + 'static,
    {
        let rpc_client = self.get_rpc_client().await?;
        let operation_result = operation(rpc_client);
        operation_result
    }

    /// Execute a swap operation with full protection
    pub async fn execute_swap_protected<T>(&self, operation: impl Fn() -> Result<T> + Send + Sync) -> Result<T>
    where
        T: Send + 'static,
    {
        operation()
    }

    /// Execute a price check operation with full protection
    pub async fn execute_price_check_protected<T>(&self, operation: impl Fn() -> Result<T> + Send + Sync) -> Result<T>
    where
        T: Send + 'static,
    {
        operation()
    }

    /// Get comprehensive system status
    pub fn get_system_status(&self) -> SystemStatus {
        SystemStatus {
            global_stats: self.globals.get_stats(),
            rate_limiter_status: self.rate_limiter.get_available_permits(),
            rpc_circuit_breaker_stats: self.rpc_circuit_breaker.stats(),
            swap_circuit_breaker_stats: self.swap_circuit_breaker.stats(),
            price_circuit_breaker_stats: self.price_circuit_breaker.stats(),
            rpc_pool_status: RpcPoolStatus {
                size: self.rpc_pool.status().size,
                available: self.rpc_pool.status().available,
                max_size: self.rpc_pool.status().max_size,
            },
        }
    }

    /// Perform health checks on all components
    pub async fn health_check(&self) -> HealthCheckResult {
        let mut issues = Vec::new();

        // Check global state
        let global_stats = self.globals.get_stats();
        if global_stats.emergency_stop {
            issues.push("Emergency stop is active".to_string());
        }
        if global_stats.is_under_stress() {
            issues.push("System is under stress".to_string());
        }

        // Check rate limiter
        let rate_limiter_status = self.rate_limiter.get_available_permits();
        if rate_limiter_status.is_under_pressure() {
            issues.push("Rate limiter is under pressure".to_string());
        }

        // Check circuit breakers
        if self.rpc_circuit_breaker.state() != crate::core::circuit_breaker::CircuitState::Closed {
            issues.push(format!("RPC circuit breaker is {:?}", self.rpc_circuit_breaker.state()));
        }
        if self.swap_circuit_breaker.state() != crate::core::circuit_breaker::CircuitState::Closed {
            issues.push(format!("Swap circuit breaker is {:?}", self.swap_circuit_breaker.state()));
        }

        // Check RPC pool
        let pool_status = self.rpc_pool.status();
        if pool_status.available == 0 {
            issues.push("No RPC connections available".to_string());
        }

        HealthCheckResult {
            healthy: issues.is_empty(),
            issues,
        }
    }

    /// Graceful shutdown
    pub async fn shutdown(&self) -> Result<()> {
        tracing::info!("Initiating graceful shutdown...");
        
        // Set emergency stop
        self.globals.set_emergency_stop(true);
        
        // Close connection pool
        self.rpc_pool.close();
        
        tracing::info!("Graceful shutdown completed");
        Ok(())
    }
}

/// System status information
#[derive(Debug, Clone)]
pub struct SystemStatus {
    pub global_stats: crate::core::lock_free_globals::GlobalStats,
    pub rate_limiter_status: crate::core::rate_limiter::RateLimiterStatus,
    pub rpc_circuit_breaker_stats: crate::core::circuit_breaker::CircuitBreakerStats,
    pub swap_circuit_breaker_stats: crate::core::circuit_breaker::CircuitBreakerStats,
    pub price_circuit_breaker_stats: crate::core::circuit_breaker::CircuitBreakerStats,
    pub rpc_pool_status: RpcPoolStatus,
}

/// RPC pool status
#[derive(Debug, Clone)]
pub struct RpcPoolStatus {
    pub size: usize,
    pub available: usize,
    pub max_size: usize,
}

/// Health check result
#[derive(Debug, Clone)]
pub struct HealthCheckResult {
    pub healthy: bool,
    pub issues: Vec<String>,
}

impl HealthCheckResult {
    /// Get a human-readable status string
    pub fn status_string(&self) -> String {
        if self.healthy {
            "System is healthy".to_string()
        } else {
            format!("System has issues: {}", self.issues.join(", "))
        }
    }
}
