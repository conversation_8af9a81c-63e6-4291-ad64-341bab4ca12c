use std::sync::atomic::{AtomicU32, AtomicU64, AtomicU8, Ordering};
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use std::future::Future;
use anyhow::Result;
use prometheus::{Counter, Gauge, register_counter, register_gauge};
use once_cell::sync::Lazy;

// Prometheus metrics for circuit breaker
static CIRCUIT_BREAKER_TRIPS: Lazy<Counter> = Lazy::new(|| {
    register_counter!("circuit_breaker_trips_total", "Total number of circuit breaker trips").unwrap()
});

static CIRCUIT_BREAKER_STATE: Lazy<Gauge> = Lazy::new(|| {
    register_gauge!("circuit_breaker_state", "Current circuit breaker state (0=Closed, 1=Open, 2=HalfOpen)").unwrap()
});

static CIRCUIT_BREAKER_FAILURES: Lazy<Counter> = Lazy::new(|| {
    register_counter!("circuit_breaker_failures_total", "Total number of failures tracked by circuit breaker").unwrap()
});

/// Circuit breaker states
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>Eq, Eq)]
pub enum CircuitState {
    Closed = 0,   // Normal operation
    Open = 1,     // Failing fast, not executing operations
    HalfOpen = 2, // Testing if service has recovered
}

impl From<u8> for CircuitState {
    fn from(value: u8) -> Self {
        match value {
            0 => CircuitState::Closed,
            1 => CircuitState::Open,
            2 => CircuitState::HalfOpen,
            _ => CircuitState::Closed,
        }
    }
}

/// Configuration for circuit breaker behavior
#[derive(Debug, Clone)]
pub struct CircuitBreakerConfig {
    pub failure_threshold: u32,
    pub recovery_timeout: Duration,
    pub half_open_max_calls: u32,
    pub half_open_success_threshold: u32,
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self {
            failure_threshold: 5,
            recovery_timeout: Duration::from_secs(60),
            half_open_max_calls: 3,
            half_open_success_threshold: 2,
        }
    }
}

impl CircuitBreakerConfig {
    /// Load configuration from environment variables
    pub fn from_env() -> Self {
        Self {
            failure_threshold: std::env::var("CIRCUIT_BREAKER_FAILURE_THRESHOLD")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(5),
            recovery_timeout: Duration::from_secs(
                std::env::var("CIRCUIT_BREAKER_RECOVERY_TIMEOUT")
                    .ok()
                    .and_then(|s| s.parse().ok())
                    .unwrap_or(60)
            ),
            half_open_max_calls: std::env::var("CIRCUIT_BREAKER_HALF_OPEN_RATE")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(3),
            half_open_success_threshold: 2, // Fixed at 2 for now
        }
    }
}

/// Circuit breaker implementation for protecting against cascading failures
#[derive(Debug)]
pub struct CircuitBreaker {
    config: CircuitBreakerConfig,
    failure_count: AtomicU32,
    success_count: AtomicU32,
    last_failure_time: AtomicU64,
    state: AtomicU8,
    half_open_calls: AtomicU32,
    name: String,
}

impl CircuitBreaker {
    /// Create a new circuit breaker with the given configuration
    pub fn new(name: String, config: CircuitBreakerConfig) -> Self {
        let breaker = Self {
            config,
            failure_count: AtomicU32::new(0),
            success_count: AtomicU32::new(0),
            last_failure_time: AtomicU64::new(0),
            state: AtomicU8::new(CircuitState::Closed as u8),
            half_open_calls: AtomicU32::new(0),
            name,
        };
        
        // Initialize metrics
        CIRCUIT_BREAKER_STATE.set(CircuitState::Closed as i64);
        
        breaker
    }

    /// Get the current state of the circuit breaker
    pub fn state(&self) -> CircuitState {
        CircuitState::from(self.state.load(Ordering::Acquire))
    }

    /// Execute an operation with circuit breaker protection
    pub async fn execute<F, Fut, T>(&self, operation: F) -> Result<T>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = Result<T>>,
    {
        match self.state() {
            CircuitState::Open => {
                if self.should_attempt_reset() {
                    self.transition_to_half_open();
                } else {
                    return Err(anyhow::anyhow!(
                        "Circuit breaker '{}' is open - failing fast", 
                        self.name
                    ));
                }
            }
            CircuitState::HalfOpen => {
                if self.half_open_calls.load(Ordering::Acquire) >= self.config.half_open_max_calls {
                    return Err(anyhow::anyhow!(
                        "Circuit breaker '{}' is half-open but max calls exceeded", 
                        self.name
                    ));
                }
                self.half_open_calls.fetch_add(1, Ordering::AcqRel);
            }
            CircuitState::Closed => {
                // Normal operation
            }
        }

        match operation().await {
            Ok(result) => {
                self.on_success();
                Ok(result)
            }
            Err(error) => {
                self.on_failure();
                Err(error)
            }
        }
    }

    /// Record a successful operation
    fn on_success(&self) {
        let current_state = self.state();
        
        match current_state {
            CircuitState::HalfOpen => {
                let success_count = self.success_count.fetch_add(1, Ordering::AcqRel) + 1;
                if success_count >= self.config.half_open_success_threshold {
                    self.transition_to_closed();
                }
            }
            CircuitState::Closed => {
                // Reset failure count on success
                self.failure_count.store(0, Ordering::Release);
            }
            CircuitState::Open => {
                // Should not happen, but handle gracefully
            }
        }
    }

    /// Record a failed operation
    fn on_failure(&self) {
        CIRCUIT_BREAKER_FAILURES.inc();
        
        let failure_count = self.failure_count.fetch_add(1, Ordering::AcqRel) + 1;
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as u64;
        
        self.last_failure_time.store(current_time, Ordering::Release);

        match self.state() {
            CircuitState::Closed => {
                if failure_count >= self.config.failure_threshold {
                    self.transition_to_open();
                }
            }
            CircuitState::HalfOpen => {
                // Any failure in half-open state transitions back to open
                self.transition_to_open();
            }
            CircuitState::Open => {
                // Already open, just update failure time
            }
        }
    }

    /// Check if we should attempt to reset the circuit breaker
    fn should_attempt_reset(&self) -> bool {
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as u64;
        
        let last_failure = self.last_failure_time.load(Ordering::Acquire);
        let time_since_failure = Duration::from_millis(current_time.saturating_sub(last_failure));
        
        time_since_failure >= self.config.recovery_timeout
    }

    /// Transition to closed state (normal operation)
    fn transition_to_closed(&self) {
        self.state.store(CircuitState::Closed as u8, Ordering::Release);
        self.failure_count.store(0, Ordering::Release);
        self.success_count.store(0, Ordering::Release);
        self.half_open_calls.store(0, Ordering::Release);
        CIRCUIT_BREAKER_STATE.set(CircuitState::Closed as i64);
        
        tracing::info!("Circuit breaker '{}' transitioned to CLOSED", self.name);
    }

    /// Transition to open state (failing fast)
    fn transition_to_open(&self) {
        self.state.store(CircuitState::Open as u8, Ordering::Release);
        self.success_count.store(0, Ordering::Release);
        self.half_open_calls.store(0, Ordering::Release);
        CIRCUIT_BREAKER_STATE.set(CircuitState::Open as i64);
        CIRCUIT_BREAKER_TRIPS.inc();
        
        tracing::warn!("Circuit breaker '{}' transitioned to OPEN", self.name);
    }

    /// Transition to half-open state (testing recovery)
    fn transition_to_half_open(&self) {
        self.state.store(CircuitState::HalfOpen as u8, Ordering::Release);
        self.success_count.store(0, Ordering::Release);
        self.half_open_calls.store(0, Ordering::Release);
        CIRCUIT_BREAKER_STATE.set(CircuitState::HalfOpen as i64);
        
        tracing::info!("Circuit breaker '{}' transitioned to HALF_OPEN", self.name);
    }

    /// Get current statistics
    pub fn stats(&self) -> CircuitBreakerStats {
        CircuitBreakerStats {
            state: self.state(),
            failure_count: self.failure_count.load(Ordering::Acquire),
            success_count: self.success_count.load(Ordering::Acquire),
            half_open_calls: self.half_open_calls.load(Ordering::Acquire),
            name: self.name.clone(),
        }
    }

    /// Force the circuit breaker to open (for testing or emergency)
    pub fn force_open(&self) {
        self.transition_to_open();
    }

    /// Force the circuit breaker to close (for testing or manual reset)
    pub fn force_close(&self) {
        self.transition_to_closed();
    }
}

/// Statistics about circuit breaker state
#[derive(Debug, Clone)]
pub struct CircuitBreakerStats {
    pub state: CircuitState,
    pub failure_count: u32,
    pub success_count: u32,
    pub half_open_calls: u32,
    pub name: String,
}

impl CircuitBreakerStats {
    /// Get a human-readable status string
    pub fn status_string(&self) -> String {
        format!(
            "{}: {:?} (failures: {}, successes: {}, half_open_calls: {})",
            self.name,
            self.state,
            self.failure_count,
            self.success_count,
            self.half_open_calls
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{sleep, Duration};

    #[tokio::test]
    async fn test_circuit_breaker_basic() {
        let config = CircuitBreakerConfig {
            failure_threshold: 2,
            recovery_timeout: Duration::from_millis(100),
            half_open_max_calls: 1,
            half_open_success_threshold: 1,
        };
        
        let cb = CircuitBreaker::new("test".to_string(), config);
        
        // Should start closed
        assert_eq!(cb.state(), CircuitState::Closed);
        
        // Successful operation should work
        let result = cb.execute(|| async { Ok::<i32, anyhow::Error>(42) }).await;
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 42);
    }

    #[tokio::test]
    async fn test_circuit_breaker_opens_on_failures() {
        let config = CircuitBreakerConfig {
            failure_threshold: 2,
            recovery_timeout: Duration::from_millis(100),
            half_open_max_calls: 1,
            half_open_success_threshold: 1,
        };
        
        let cb = CircuitBreaker::new("test".to_string(), config);
        
        // First failure
        let result = cb.execute(|| async { Err::<i32, anyhow::Error>(anyhow::anyhow!("test error")) }).await;
        assert!(result.is_err());
        assert_eq!(cb.state(), CircuitState::Closed);
        
        // Second failure should open the circuit
        let result = cb.execute(|| async { Err::<i32, anyhow::Error>(anyhow::anyhow!("test error")) }).await;
        assert!(result.is_err());
        assert_eq!(cb.state(), CircuitState::Open);
        
        // Next call should fail fast
        let result = cb.execute(|| async { Ok::<i32, anyhow::Error>(42) }).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("failing fast"));
    }

    #[tokio::test]
    async fn test_circuit_breaker_recovery() {
        let config = CircuitBreakerConfig {
            failure_threshold: 1,
            recovery_timeout: Duration::from_millis(50),
            half_open_max_calls: 1,
            half_open_success_threshold: 1,
        };
        
        let cb = CircuitBreaker::new("test".to_string(), config);
        
        // Cause failure to open circuit
        let _ = cb.execute(|| async { Err::<i32, anyhow::Error>(anyhow::anyhow!("test error")) }).await;
        assert_eq!(cb.state(), CircuitState::Open);
        
        // Wait for recovery timeout
        sleep(Duration::from_millis(60)).await;
        
        // Next call should transition to half-open and succeed
        let result = cb.execute(|| async { Ok::<i32, anyhow::Error>(42) }).await;
        assert!(result.is_ok());
        assert_eq!(cb.state(), CircuitState::Closed);
    }
}
