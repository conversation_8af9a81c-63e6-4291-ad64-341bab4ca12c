use anyhow::Result;
use anchor_client::solana_sdk::{
    instruction::Instruction,
    pubkey::Pubkey,
    signature::Signature,
    transaction::Transaction,
};

pub struct TransactionBuilder {
    instructions: Vec<Instruction>,
    payer: Option<Pubkey>,
}

impl TransactionBuilder {
    pub fn new() -> Self {
        Self {
            instructions: Vec::new(),
            payer: None,
        }
    }

    pub fn add_instruction(&mut self, instruction: Instruction) -> &mut Self {
        self.instructions.push(instruction);
        self
    }

    pub fn set_payer(&mut self, payer: Pubkey) -> &mut Self {
        self.payer = Some(payer);
        self
    }

    pub fn build(&self) -> Result<Transaction> {
        let payer = self.payer.ok_or_else(|| anyhow::anyhow!("Payer not set"))?;
        
        let mut transaction = Transaction::new_with_payer(&self.instructions, Some(&payer));
        Ok(transaction)
    }
}

impl Default for TransactionBuilder {
    fn default() -> Self {
        Self::new()
    }
}
