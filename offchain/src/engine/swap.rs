use anyhow::Result;
use anchor_client::solana_sdk::pubkey::Pubkey;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub enum SwapDirection {
    AToB,
    BToA,
}

#[derive(Debug, <PERSON><PERSON>, Copy)]
pub enum SwapInType {
    ExactIn,
    ExactOut,
}

pub struct Pump {
    pub mint: Pubkey,
    pub bonding_curve: Pubkey,
}

impl Pump {
    pub fn new(mint: Pubkey, bonding_curve: Pubkey) -> Self {
        Self { mint, bonding_curve }
    }
}
