use anyhow::Result;
use anchor_client::solana_client::rpc_client::RpcClient;
use anchor_client::solana_sdk::{
    account::Account,
    pubkey::Pubkey,
};
use anchor_client::solana_client::rpc_config::{RpcAccountInfoConfig, RpcProgramAccountsConfig};
use anchor_client::solana_client::rpc_filter::{RpcFilterType, Memcmp};
use std::sync::Arc;

pub async fn get_program_acccounts_with_filter_async(
    rpc_client: &RpcClient,
    program_id: &Pubkey,
    filters: Vec<RpcFilterType>,
) -> Result<Vec<(Pubkey, Account)>> {
    let config = RpcProgramAccountsConfig {
        filters: Some(filters),
        account_config: RpcAccountInfoConfig {
            encoding: Some(solana_account_decoder::UiAccountEncoding::Base64),
            data_slice: None,
            commitment: None,
            min_context_slot: None,
        },
        with_context: None,
    };

    let accounts = rpc_client
        .get_program_accounts_with_config(program_id, config)?;

    Ok(accounts)
}

pub fn get_program_acccounts_with_filter(
    rpc_client: &RpcClient,
    program_id: &Pubkey,
    filters: Vec<RpcFilterType>,
) -> Result<Vec<(Pubkey, Account)>> {
    let config = RpcProgramAccountsConfig {
        filters: Some(filters),
        account_config: RpcAccountInfoConfig {
            encoding: Some(solana_account_decoder::UiAccountEncoding::Base64),
            data_slice: None,
            commitment: None,
            min_context_slot: None,
        },
        with_context: None,
    };

    let accounts = rpc_client
        .get_program_accounts_with_config(program_id, config)?;

    Ok(accounts)
}
