use anchor_client::solana_sdk::pubkey::Pubkey;
use anyhow::Result;

/// Generic Pool trait for all DEX pools
pub trait Pool {
    /// Get the unique identifier for this pool
    fn get_id(&self) -> Pubkey;
    
    /// Get the name of the DEX this pool belongs to
    fn get_dex_name(&self) -> &str;
    
    /// Get the mint address of token A
    fn get_token_a_mint(&self) -> Pubkey;
    
    /// Get the mint address of token B
    fn get_token_b_mint(&self) -> Pubkey;
    
    /// Get the reserve account for token A
    fn get_token_a_reserve(&self) -> Pubkey;
    
    /// Get the reserve account for token B
    fn get_token_b_reserve(&self) -> Pubkey;
    
    /// Get the current price of token A in terms of token B
    fn get_token_price(&self) -> f64;
    
    /// Calculate output amount for a given input amount
    fn calculate_swap_output(&self, input_amount: u64, input_is_token_a: bool) -> Result<u64>;
    
    /// Get the fee rate for this pool in basis points
    fn get_fee_rate(&self) -> u16;
    
    /// Check if the pool is currently active and tradeable
    fn is_active(&self) -> bool;
    
    /// Get the type of pool (e.g., "constant_product", "stable_curve", "concentrated_liquidity")
    fn get_pool_type(&self) -> &str;
}
