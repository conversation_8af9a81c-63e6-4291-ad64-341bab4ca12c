# DEX Integration Reference Guide

This document provides a comprehensive reference for integrating with major Solana DEXes in our arbitrage bot.

## Raydium Integration

### Resources
- **Raydium SDK V2**: https://crates.io/crates/raydium-sdk-V2
- **Rust Swap Implementation**: https://solana.stackexchange.com/questions/14259/raydium-swap-with-rust
- **Official GitHub**: https://github.com/raydium-io

### Key Implementation Details
- **Program IDs**: 
  - AMM: `675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8`
  - CLMM: `CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK`
  - CPMM: `CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C`

- **Pool Structure**: Raydium pools contain token reserves, fees, and swap calculations
- **Swap Calculation**: Uses constant product formula with fees
- **Account Fetching**: Use `get_program_accounts` with proper filters for pool discovery

### Code Patterns
```rust
// Pool fetching with filters
let pools = get_program_accounts_with_filter(
    &rpc_client,
    &program_id,
    pool_size,
    &token_mint_position,
    &mint_pubkey
);

// Swap calculation
fn calculate_swap_output(&self, input_amount: u64, input_is_token_a: bool) -> Result<u64> {
    // Implement constant product formula with fees
}
```

## Orca Whirlpools Integration

### Resources
- **Official GitHub**: https://github.com/orca-so/whirlpools
- **Documentation**: https://dev.orca.so/orca_whirlpools_docs/
- **Rust Client**: https://crates.io/crates/orca_whirlpools_client

### Key Implementation Details
- **Program ID**: `whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc`
- **Concentrated Liquidity**: Uses tick-based pricing model
- **Price Calculation**: Based on sqrt price and tick spacing
- **Fee Structure**: Multiple fee tiers (0.01%, 0.05%, 0.3%, 1%)

### Code Patterns
```rust
use orca_whirlpools_client::*;

// Whirlpool client initialization
let whirlpool_client = WhirlpoolClient::new(rpc_client);

// Price calculation for concentrated liquidity
fn get_price_from_sqrt_price(sqrt_price: u128) -> f64 {
    let price = (sqrt_price as f64 / (2_u128.pow(64) as f64)).powi(2);
    price
}
```

## Meteora DLMM Integration

### Resources
- **DLMM SDK**: https://github.com/MeteoraAg/dlmm-sdk
- **Documentation**: https://docs.meteora.ag/integration/dynamic-vault-integration/using-rust-client

### Key Implementation Details
- **Program ID**: `LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo`
- **Dynamic Liquidity**: Bin-based liquidity distribution
- **Fee Structure**: Dynamic fees based on volatility
- **Bin Management**: Active bins for current price range

### Code Patterns
```rust
// DLMM pool interaction
pub struct DLMMPool {
    pub bin_step: u16,
    pub active_bin_id: i32,
    pub reserve_x: u64,
    pub reserve_y: u64,
}

// Swap calculation for DLMM
fn calculate_dlmm_swap(&self, amount_in: u64, swap_for_y: bool) -> Result<u64> {
    // Implement bin-based swap calculation
}
```

## Common Integration Patterns

### Pool Discovery
```rust
// Generic pool fetching function
pub async fn get_program_accounts_with_filter_async(
    rpc_client: &RpcClient,
    program_id: &Pubkey,
    filters: Vec<RpcFilterType>,
) -> Result<Vec<(Pubkey, Account)>> {
    let config = RpcProgramAccountsConfig {
        filters: Some(filters),
        account_config: RpcAccountInfoConfig {
            encoding: Some(UiAccountEncoding::Base64),
            ..Default::default()
        },
        sort_results: Some(true),
    };
    
    rpc_client.get_program_accounts_with_config(program_id, config).await
}
```

### Swap Output Calculation
```rust
pub trait Pool {
    fn get_token_a_mint(&self) -> Pubkey;
    fn get_token_b_mint(&self) -> Pubkey;
    fn get_token_price(&self) -> Result<f64>;
    fn calculate_swap_output(&self, input_amount: u64, input_is_token_a: bool) -> Result<u64>;
    fn get_estimated_output_amount(&self, input_amount: u64, is_a_to_b: bool) -> Result<u64>;
}
```

### Error Handling
```rust
#[derive(Debug, thiserror::Error)]
pub enum SwapError {
    #[error("Calculation overflow")]
    CalculationOverflow,
    #[error("Invalid input: {0}")]
    InvalidInput(String),
    #[error("Insufficient liquidity")]
    InsufficientLiquidity,
    #[error("Slippage exceeded")]
    SlippageExceeded,
    #[error("Pool not found")]
    PoolNotFound,
    #[error("Invalid pool state")]
    InvalidPoolState,
    #[error("Math error: {0}")]
    MathError(String),
}
```

## Dependencies Required

```toml
[dependencies]
# Raydium
raydium-sdk-V2 = "0.1.0"

# Orca
orca_whirlpools_client = "0.1.0"

# Meteora (typically integrated via direct program calls)
# No specific crate, use anchor-client for program interaction

# Common Solana dependencies
anchor-client = "0.31.0"
solana-client = "2.1.14"
spl-token = "4.0.0"
arrayref = "0.3.8"
num_enum = "0.7.3"
```

## Implementation Checklist

- [ ] Implement Pool trait for each DEX
- [ ] Add calculate_swap_output method to all pool implementations
- [ ] Create proper error handling for each DEX
- [ ] Implement pool discovery functions
- [ ] Add price calculation methods
- [ ] Handle different fee structures
- [ ] Implement slippage protection
- [ ] Add proper logging and monitoring
