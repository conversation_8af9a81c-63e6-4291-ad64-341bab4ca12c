[package]
name = "solana-vntr-sniper"
version = "0.1.0"
edition = "2021"

[dependencies]
solana-client = { version = "2.1.14" }

dotenv = "0.15"
chrono = "0.4.26"
clap = { version = "4.5.7", features = ["derive"] }
anyhow = "1.0.62"
serde = "1.0.145"
serde_json = "1.0.86"
tokio = { version = "1.21.2", features = ["full"] }
tokio-tungstenite = { version = "0.23.1", features = ["native-tls"] }
tokio-stream = "0.1.11"
anchor-client = { version = "0.31.0", features = ["async"] }
anchor-lang = "=0.31.0"
yellowstone-grpc-client = "4.1.0"
yellowstone-grpc-proto = "4.1.1"
spl-token = { version = "4.0.0", features = ["no-entrypoint"] }
spl-token-2022 = { version = "4.0.0", features = ["no-entrypoint"] }
spl-associated-token-account = { version = "4.0.0", features = [
    "no-entrypoint",
] }
base64 = "0.13"
rand = "0.8.5"
borsh = { version = "1.5.3"}
borsh-derive = "1.5.3"
colored = "3.0.0"
reqwest = { version = "0.11.27", features = ["json", "socks", "native-tls"] }
lazy_static = "1.5.0"
once_cell = "1.19.0"
bs58 = "0.4"
bincode = "1.3.3"

tokio-js-set-interval = "1.3.0"
bytemuck = "1.21.0"
indicatif = "0.17.8"
tracing = "0.1.40"
futures-util = "0.3.30"
maplit = "1.0.2"
jito-json-rpc-client = { git = "https://github.com/jwest951227/jito-block-engine-json-rpc-client.git", branch="v2.1.1", package = "jito-block-engine-json-rpc-client" }
futures = "0.3.31"
teloxide = { version = "0.12", features = ["macros"] }
# New dependencies for improved concurrency and reliability
dashmap = "6.1.0"
arc-swap = "1.7.1"
deadpool = { version = "0.12.1", features = ["managed", "rt_tokio_1"] }
prometheus = { version = "0.13.4", features = ["process"] }
thiserror = "1.0.63"
async-trait = "0.1.83"
arrayref = "0.3.8"
num_enum = "0.7.3"
spl-math = "0.1.0"